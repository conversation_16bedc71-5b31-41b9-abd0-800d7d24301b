html,
body,
#root {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
    filter: invert(80%);
}

.ant-layout {
    min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
    left: unset;
}

canvas {
    display: block;
}

body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
    list-style: none;
}

@media (max-width: 768px) {
    .ant-table {
        width: 100%;
        overflow-x: auto;

        &-thead > tr,
        &-tbody > tr {
            > th,
            > td {
                white-space: pre;

                > span {
                    display: block;
                }
            }
        }
    }
}
